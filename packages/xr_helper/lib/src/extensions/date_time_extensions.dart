part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatDateToTimeAndString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm', 'en').format(this!);
  }

  // time
  String get formatDateToTime {
    if (this == null) return '';
    return DateFormat('HH:mm', 'en').format(this!);
  }

  // month name
  String get formatToMonthName {
    if (this == null) return '';
    return DateFormat('MMMM', 'en').format(this!);
  }

  bool get isCurrentMonth {
    if (this == null) return false;
    final now = DateTime.now();
    return now.month.toString().padLeft(2, '0') ==
        this!.month.toString().padLeft(2, '0');
  }

  String get formatToDay {
    if (this == null) return '';
    return DateFormat('EEEE', 'en').format(this!);
  }

  //? isToday
  bool get isToday {
    if (this == null) return false;
    final now = DateTime.now();
    return now.day == this!.day &&
        now.month == this!.month &&
        now.year == this!.year;
  }
}
