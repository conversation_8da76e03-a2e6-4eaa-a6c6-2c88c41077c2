// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Speak with confidence`
  String get speakWithConfidence {
    return Intl.message(
      'Speak with confidence',
      name: 'speakWithConfidence',
      desc: '',
      args: [],
    );
  }

  /// `Every day is a journey. \nSign in to join us.`
  String get getTalkingFrom {
    return Intl.message(
      'Every day is a journey. \nSign in to join us.',
      name: 'getTalkingFrom',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up`
  String get signUp {
    return Intl.message('Sign Up', name: 'signUp', desc: '', args: []);
  }

  /// `Already have an account?`
  String get alreadyHaveAnAccount {
    return Intl.message(
      'Already have an account?',
      name: 'alreadyHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `You don’t have account?`
  String get dontHaveAnAccount {
    return Intl.message(
      'You don’t have account?',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `No questions`
  String get noQuestions {
    return Intl.message(
      'No questions',
      name: 'noQuestions',
      desc: '',
      args: [],
    );
  }

  /// `Question`
  String get question {
    return Intl.message('Question', name: 'question', desc: '', args: []);
  }

  /// `Exams`
  String get exams {
    return Intl.message('Exams', name: 'exams', desc: '', args: []);
  }

  /// `Add New Question`
  String get addNewQuestion {
    return Intl.message(
      'Add New Question',
      name: 'addNewQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Sessions`
  String get sessions {
    return Intl.message('Sessions', name: 'sessions', desc: '', args: []);
  }

  /// `Are you sure to delete this question ?`
  String get areYouSureToDeleteThisQuestion {
    return Intl.message(
      'Are you sure to delete this question ?',
      name: 'areYouSureToDeleteThisQuestion',
      desc: '',
      args: [],
    );
  }

  /// `You cannot delete this question because it's has student results`
  String get youCannotDeleteThisQuestionBecauseitsHasStudentResults {
    return Intl.message(
      'You cannot delete this question because it\'s has student results',
      name: 'youCannotDeleteThisQuestionBecauseitsHasStudentResults',
      desc: '',
      args: [],
    );
  }

  /// `Sign In`
  String get signIn {
    return Intl.message('Sign In', name: 'signIn', desc: '', args: []);
  }

  /// `Sign up as a`
  String get SignupAsa {
    return Intl.message('Sign up as a', name: 'SignupAsa', desc: '', args: []);
  }

  /// `Administrator`
  String get administrator {
    return Intl.message(
      'Administrator',
      name: 'administrator',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get history {
    return Intl.message('History', name: 'history', desc: '', args: []);
  }

  /// `Results`
  String get results {
    return Intl.message('Results', name: 'results', desc: '', args: []);
  }

  /// `Teacher`
  String get teacher {
    return Intl.message('Teacher', name: 'teacher', desc: '', args: []);
  }

  /// `Search Question`
  String get searchQuestion {
    return Intl.message(
      'Search Question',
      name: 'searchQuestion',
      desc: '',
      args: [],
    );
  }

  /// `Phone Number`
  String get phoneNumber {
    return Intl.message(
      'Phone Number',
      name: 'phoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `All Students`
  String get allStudents {
    return Intl.message(
      'All Students',
      name: 'allStudents',
      desc: '',
      args: [],
    );
  }

  /// `Administrator Sign up`
  String get adminSignUp {
    return Intl.message(
      'Administrator Sign up',
      name: 'adminSignUp',
      desc: '',
      args: [],
    );
  }

  /// `Teacher Sign up`
  String get teacherSignUp {
    return Intl.message(
      'Teacher Sign up',
      name: 'teacherSignUp',
      desc: '',
      args: [],
    );
  }

  /// `Nursery Logo`
  String get nurseryLogo {
    return Intl.message(
      'Nursery Logo',
      name: 'nurseryLogo',
      desc: '',
      args: [],
    );
  }

  /// `Nursery Name`
  String get nurseryName {
    return Intl.message(
      'Nursery Name',
      name: 'nurseryName',
      desc: '',
      args: [],
    );
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `I have read the`
  String get iHaveReadThe {
    return Intl.message(
      'I have read the',
      name: 'iHaveReadThe',
      desc: '',
      args: [],
    );
  }

  /// `Subscriptions`
  String get subscriptions {
    return Intl.message(
      'Subscriptions',
      name: 'subscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Active Students`
  String get activeStudents {
    return Intl.message(
      'Active Students',
      name: 'activeStudents',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `Finish, let’s start`
  String get finishLetsStart {
    return Intl.message(
      'Finish, let’s start',
      name: 'finishLetsStart',
      desc: '',
      args: [],
    );
  }

  /// `Upload logo`
  String get uploadLogo {
    return Intl.message('Upload logo', name: 'uploadLogo', desc: '', args: []);
  }

  /// `Setup your classes`
  String get setupYourClasses {
    return Intl.message(
      'Setup your classes',
      name: 'setupYourClasses',
      desc: '',
      args: [],
    );
  }

  /// `Add New Class`
  String get addNewClass {
    return Intl.message(
      'Add New Class',
      name: 'addNewClass',
      desc: '',
      args: [],
    );
  }

  /// `Edit Class`
  String get editClass {
    return Intl.message('Edit Class', name: 'editClass', desc: '', args: []);
  }

  /// `Skip for now`
  String get SkipForNow {
    return Intl.message('Skip for now', name: 'SkipForNow', desc: '', args: []);
  }

  /// `Good Morning!`
  String get goodMorning {
    return Intl.message(
      'Good Morning!',
      name: 'goodMorning',
      desc: '',
      args: [],
    );
  }

  /// `Good Afternoon!`
  String get goodAfternoon {
    return Intl.message(
      'Good Afternoon!',
      name: 'goodAfternoon',
      desc: '',
      args: [],
    );
  }

  /// `Classes`
  String get classes {
    return Intl.message('Classes', name: 'classes', desc: '', args: []);
  }

  /// `Class Name`
  String get className {
    return Intl.message('Class Name', name: 'className', desc: '', args: []);
  }

  /// `Class Description`
  String get classDescription {
    return Intl.message(
      'Class Description',
      name: 'classDescription',
      desc: '',
      args: [],
    );
  }

  /// `Team`
  String get team {
    return Intl.message('Team', name: 'team', desc: '', args: []);
  }

  /// `Students`
  String get students {
    return Intl.message('Students', name: 'students', desc: '', args: []);
  }

  /// `Activities`
  String get activities {
    return Intl.message('Activities', name: 'activities', desc: '', args: []);
  }

  /// `Staff`
  String get staff {
    return Intl.message('Staff', name: 'staff', desc: '', args: []);
  }

  /// `Add Students`
  String get addStudents {
    return Intl.message(
      'Add Students',
      name: 'addStudents',
      desc: '',
      args: [],
    );
  }

  /// `Add New Students`
  String get addNewStudents {
    return Intl.message(
      'Add New Students',
      name: 'addNewStudents',
      desc: '',
      args: [],
    );
  }

  /// `Skip for Now`
  String get skipForNow {
    return Intl.message('Skip for Now', name: 'skipForNow', desc: '', args: []);
  }

  /// `Add Nursery Team`
  String get addNurseryTeam {
    return Intl.message(
      'Add Nursery Team',
      name: 'addNurseryTeam',
      desc: '',
      args: [],
    );
  }

  /// `Add New Team Member`
  String get addNurseryTeamMember {
    return Intl.message(
      'Add New Team Member',
      name: 'addNurseryTeamMember',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Let’s Start`
  String get letsStart {
    return Intl.message('Let’s Start', name: 'letsStart', desc: '', args: []);
  }

  /// `Add a Nursery Activities`
  String get nurseryActivities {
    return Intl.message(
      'Add a Nursery Activities',
      name: 'nurseryActivities',
      desc: '',
      args: [],
    );
  }

  /// `Add New Activity `
  String get addNurseryActivities {
    return Intl.message(
      'Add New Activity ',
      name: 'addNurseryActivities',
      desc: '',
      args: [],
    );
  }

  /// `Max upload images is only 4`
  String get maxUploadFilesIsOnly4 {
    return Intl.message(
      'Max upload images is only 4',
      name: 'maxUploadFilesIsOnly4',
      desc: '',
      args: [],
    );
  }

  /// `Max upload image size is only 5 MB`
  String get maxUploadFileSizeIsOnly5MB {
    return Intl.message(
      'Max upload image size is only 5 MB',
      name: 'maxUploadFileSizeIsOnly5MB',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations`
  String get congratulations {
    return Intl.message(
      'Congratulations',
      name: 'congratulations',
      desc: '',
      args: [],
    );
  }

  /// `Let’s do a great job`
  String get letsDoAGreatJob {
    return Intl.message(
      'Let’s do a great job',
      name: 'letsDoAGreatJob',
      desc: '',
      args: [],
    );
  }

  /// `Dashboard`
  String get dashboard {
    return Intl.message('Dashboard', name: 'dashboard', desc: '', args: []);
  }

  /// `Messages`
  String get messages {
    return Intl.message('Messages', name: 'messages', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Attendees of Today`
  String get attendeesOfToday {
    return Intl.message(
      'Attendees of Today',
      name: 'attendeesOfToday',
      desc: '',
      args: [],
    );
  }

  /// `Of`
  String get Of {
    return Intl.message('Of', name: 'Of', desc: '', args: []);
  }

  /// `Current Activity`
  String get currentActivity {
    return Intl.message(
      'Current Activity',
      name: 'currentActivity',
      desc: '',
      args: [],
    );
  }

  /// `Attendance`
  String get attendance {
    return Intl.message('Attendance', name: 'attendance', desc: '', args: []);
  }

  /// `Financial`
  String get financial {
    return Intl.message('Financial', name: 'financial', desc: '', args: []);
  }

  /// `Emergency`
  String get emergency {
    return Intl.message('Emergency', name: 'emergency', desc: '', args: []);
  }

  /// `Events`
  String get events {
    return Intl.message('Events', name: 'events', desc: '', args: []);
  }

  /// `Members`
  String get members {
    return Intl.message('Members', name: 'members', desc: '', args: []);
  }

  /// `Event this month`
  String get eventThisMonth {
    return Intl.message(
      'Event this month',
      name: 'eventThisMonth',
      desc: '',
      args: [],
    );
  }

  /// `Create new class`
  String get createNewClass {
    return Intl.message(
      'Create new class',
      name: 'createNewClass',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get back {
    return Intl.message('Back', name: 'back', desc: '', args: []);
  }

  /// `My Classes`
  String get myClasses {
    return Intl.message('My Classes', name: 'myClasses', desc: '', args: []);
  }

  /// `My Class`
  String get myClass {
    return Intl.message('My Class', name: 'myClass', desc: '', args: []);
  }

  /// `Teacher info`
  String get teacherInfo {
    return Intl.message(
      'Teacher info',
      name: 'teacherInfo',
      desc: '',
      args: [],
    );
  }

  /// `Daily Schedule`
  String get dailySchedule {
    return Intl.message(
      'Daily Schedule',
      name: 'dailySchedule',
      desc: '',
      args: [],
    );
  }

  /// `Pick Image`
  String get pickImage {
    return Intl.message('Pick Image', name: 'pickImage', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Create a new class`
  String get createANewClass {
    return Intl.message(
      'Create a new class',
      name: 'createANewClass',
      desc: '',
      args: [],
    );
  }

  /// `Add a new Supply`
  String get createANewSupply {
    return Intl.message(
      'Add a new Supply',
      name: 'createANewSupply',
      desc: '',
      args: [],
    );
  }

  /// `Add a new staff member`
  String get addANewStaffMember {
    return Intl.message(
      'Add a new staff member',
      name: 'addANewStaffMember',
      desc: '',
      args: [],
    );
  }

  /// `Teacher Name`
  String get teacherName {
    return Intl.message(
      'Teacher Name',
      name: 'teacherName',
      desc: '',
      args: [],
    );
  }

  /// `supply Name`
  String get supplyName {
    return Intl.message('supply Name', name: 'supplyName', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Please pick an image`
  String get pleasePickAnImage {
    return Intl.message(
      'Please pick an image',
      name: 'pleasePickAnImage',
      desc: '',
      args: [],
    );
  }

  /// `Student Name`
  String get studentName {
    return Intl.message(
      'Student Name',
      name: 'studentName',
      desc: '',
      args: [],
    );
  }

  /// `Mather Phone number`
  String get matherPhoneNumber {
    return Intl.message(
      'Mather Phone number',
      name: 'matherPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Home Address`
  String get homeAddress {
    return Intl.message(
      'Home Address',
      name: 'homeAddress',
      desc: '',
      args: [],
    );
  }

  /// `Add Parents Phone number`
  String get addParentsPhoneNumber {
    return Intl.message(
      'Add Parents Phone number',
      name: 'addParentsPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Birth Date`
  String get birthDate {
    return Intl.message('Birth Date', name: 'birthDate', desc: '', args: []);
  }

  /// `Activity Name`
  String get activityName {
    return Intl.message(
      'Activity Name',
      name: 'activityName',
      desc: '',
      args: [],
    );
  }

  /// `Activity Description`
  String get activityDescription {
    return Intl.message(
      'Activity Description',
      name: 'activityDescription',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `Mother Phone number`
  String get motherPhoneNumber {
    return Intl.message(
      'Mother Phone number',
      name: 'motherPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Parent Phone number`
  String get parentPhoneNumber {
    return Intl.message(
      'Parent Phone number',
      name: 'parentPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Attendance chart`
  String get attendanceChart {
    return Intl.message(
      'Attendance chart',
      name: 'attendanceChart',
      desc: '',
      args: [],
    );
  }

  /// `Student & Class`
  String get studentAndClass {
    return Intl.message(
      'Student & Class',
      name: 'studentAndClass',
      desc: '',
      args: [],
    );
  }

  /// `Select period`
  String get selectPeriod {
    return Intl.message(
      'Select period',
      name: 'selectPeriod',
      desc: '',
      args: [],
    );
  }

  /// `attended`
  String get attended {
    return Intl.message('attended', name: 'attended', desc: '', args: []);
  }

  /// `absent`
  String get absent {
    return Intl.message('absent', name: 'absent', desc: '', args: []);
  }

  /// `Income chart`
  String get incomeChart {
    return Intl.message(
      'Income chart',
      name: 'incomeChart',
      desc: '',
      args: [],
    );
  }

  /// `Activities completed`
  String get activitiesCompleted {
    return Intl.message(
      'Activities completed',
      name: 'activitiesCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Bills chart`
  String get billsChart {
    return Intl.message('Bills chart', name: 'billsChart', desc: '', args: []);
  }

  /// `Income chart`
  String get invoicesChart {
    return Intl.message(
      'Income chart',
      name: 'invoicesChart',
      desc: '',
      args: [],
    );
  }

  /// `Current month`
  String get currentMonth {
    return Intl.message(
      'Current month',
      name: 'currentMonth',
      desc: '',
      args: [],
    );
  }

  /// `Assign to class`
  String get assignToClass {
    return Intl.message(
      'Assign to class',
      name: 'assignToClass',
      desc: '',
      args: [],
    );
  }

  /// `Assign`
  String get assign {
    return Intl.message('Assign', name: 'assign', desc: '', args: []);
  }

  /// `Assigned`
  String get assigned {
    return Intl.message('Assigned', name: 'assigned', desc: '', args: []);
  }

  /// `Income`
  String get invoices {
    return Intl.message('Income', name: 'invoices', desc: '', args: []);
  }

  /// `Bills`
  String get bills {
    return Intl.message('Bills', name: 'bills', desc: '', args: []);
  }

  /// `Add New Bill`
  String get addNewBill {
    return Intl.message('Add New Bill', name: 'addNewBill', desc: '', args: []);
  }

  /// `From`
  String get from {
    return Intl.message('From', name: 'from', desc: '', args: []);
  }

  /// `To`
  String get to {
    return Intl.message('To', name: 'to', desc: '', args: []);
  }

  /// `Add New Income`
  String get addNewInvoice {
    return Intl.message(
      'Add New Income',
      name: 'addNewInvoice',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Delete`
  String get delete {
    return Intl.message('Delete', name: 'delete', desc: '', args: []);
  }

  /// `Date`
  String get date {
    return Intl.message('Date', name: 'date', desc: '', args: []);
  }

  /// `Bill Name`
  String get billName {
    return Intl.message('Bill Name', name: 'billName', desc: '', args: []);
  }

  /// `Bill Amount`
  String get billAmount {
    return Intl.message('Bill Amount', name: 'billAmount', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Confirmation`
  String get confirmation {
    return Intl.message(
      'Confirmation',
      name: 'confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Are you sure to delete this bill ?`
  String get areYouSureToDeleteThisBill {
    return Intl.message(
      'Are you sure to delete this bill ?',
      name: 'areYouSureToDeleteThisBill',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this Income ?`
  String get areYouSureToDeleteThisInvoice {
    return Intl.message(
      'Are you sure to delete this Income ?',
      name: 'areYouSureToDeleteThisInvoice',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this class ?`
  String get areYouSureToDeleteThisClass {
    return Intl.message(
      'Are you sure to delete this class ?',
      name: 'areYouSureToDeleteThisClass',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this supply ?`
  String get areYouSureToDeleteThisSupply {
    return Intl.message(
      'Are you sure to delete this supply ?',
      name: 'areYouSureToDeleteThisSupply',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this teacher ?`
  String get areYouSureToDeleteThisTeacher {
    return Intl.message(
      'Are you sure to delete this teacher ?',
      name: 'areYouSureToDeleteThisTeacher',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this student ?`
  String get areYouSureToDeleteThisStudent {
    return Intl.message(
      'Are you sure to delete this student ?',
      name: 'areYouSureToDeleteThisStudent',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete this activity ?`
  String get areYouSureToDeleteThisActivity {
    return Intl.message(
      'Are you sure to delete this activity ?',
      name: 'areYouSureToDeleteThisActivity',
      desc: '',
      args: [],
    );
  }

  /// `Income Amount`
  String get invoiceAmount {
    return Intl.message(
      'Income Amount',
      name: 'invoiceAmount',
      desc: '',
      args: [],
    );
  }

  /// `Income Name`
  String get invoiceName {
    return Intl.message('Income Name', name: 'invoiceName', desc: '', args: []);
  }

  /// `deleted successfully`
  String get deletedSuccessfully {
    return Intl.message(
      'deleted successfully',
      name: 'deletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Edit successfully`
  String get editSuccessfully {
    return Intl.message(
      'Edit successfully',
      name: 'editSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Edit Teacher`
  String get editTeacher {
    return Intl.message(
      'Edit Teacher',
      name: 'editTeacher',
      desc: '',
      args: [],
    );
  }

  /// `Added successfully`
  String get addedSuccessfully {
    return Intl.message(
      'Added successfully',
      name: 'addedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Didn’t get the code?`
  String get didNotGetCode {
    return Intl.message(
      'Didn’t get the code?',
      name: 'didNotGetCode',
      desc: '',
      args: [],
    );
  }

  /// `Resend Code`
  String get resendCode {
    return Intl.message('Resend Code', name: 'resendCode', desc: '', args: []);
  }

  /// `Complete Verification`
  String get completeVerification {
    return Intl.message(
      'Complete Verification',
      name: 'completeVerification',
      desc: '',
      args: [],
    );
  }

  /// `Error occurred`
  String get errorOccurred {
    return Intl.message(
      'Error occurred',
      name: 'errorOccurred',
      desc: '',
      args: [],
    );
  }

  /// `Verification code is wrong`
  String get verificationCodeIsWrong {
    return Intl.message(
      'Verification code is wrong',
      name: 'verificationCodeIsWrong',
      desc: '',
      args: [],
    );
  }

  /// `Enter OTP`
  String get enterOtp {
    return Intl.message('Enter OTP', name: 'enterOtp', desc: '', args: []);
  }

  /// `We sent a verification code to`
  String get sentVerificationCode {
    return Intl.message(
      'We sent a verification code to',
      name: 'sentVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `verify`
  String get verify {
    return Intl.message('verify', name: 'verify', desc: '', args: []);
  }

  /// `Enter valid phone number`
  String get enterValidPhoneNumber {
    return Intl.message(
      'Enter valid phone number',
      name: 'enterValidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `First enter your phone number`
  String get enterPhoneNumberFirst {
    return Intl.message(
      'First enter your phone number',
      name: 'enterPhoneNumberFirst',
      desc: '',
      args: [],
    );
  }

  /// `Verification successful`
  String get verificationSuccessful {
    return Intl.message(
      'Verification successful',
      name: 'verificationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Please accept terms`
  String get pleaseAcceptTerms {
    return Intl.message(
      'Please accept terms',
      name: 'pleaseAcceptTerms',
      desc: '',
      args: [],
    );
  }

  /// `Please verify phone`
  String get pleaseVerifyPhone {
    return Intl.message(
      'Please verify phone',
      name: 'pleaseVerifyPhone',
      desc: '',
      args: [],
    );
  }

  /// `No events`
  String get noEvents {
    return Intl.message('No events', name: 'noEvents', desc: '', args: []);
  }

  /// `No Bills`
  String get noBills {
    return Intl.message('No Bills', name: 'noBills', desc: '', args: []);
  }

  /// `No Income`
  String get noInvoices {
    return Intl.message('No Income', name: 'noInvoices', desc: '', args: []);
  }

  /// `No Classes`
  String get noClasses {
    return Intl.message('No Classes', name: 'noClasses', desc: '', args: []);
  }

  /// `No Notifications`
  String get noNotifications {
    return Intl.message(
      'No Notifications',
      name: 'noNotifications',
      desc: '',
      args: [],
    );
  }

  /// `No Teachers`
  String get noTeachers {
    return Intl.message('No Teachers', name: 'noTeachers', desc: '', args: []);
  }

  /// `No Students`
  String get noStudents {
    return Intl.message('No Students', name: 'noStudents', desc: '', args: []);
  }

  /// `No Activities`
  String get noActivities {
    return Intl.message(
      'No Activities',
      name: 'noActivities',
      desc: '',
      args: [],
    );
  }

  /// `No Supplies`
  String get noSupplies {
    return Intl.message('No Supplies', name: 'noSupplies', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Add New Event`
  String get addNewEvent {
    return Intl.message(
      'Add New Event',
      name: 'addNewEvent',
      desc: '',
      args: [],
    );
  }

  /// `Event Name`
  String get eventName {
    return Intl.message('Event Name', name: 'eventName', desc: '', args: []);
  }

  /// `Event Type`
  String get eventType {
    return Intl.message('Event Type', name: 'eventType', desc: '', args: []);
  }

  /// `Mother`
  String get mother {
    return Intl.message('Mother', name: 'mother', desc: '', args: []);
  }

  /// `Father`
  String get father {
    return Intl.message('Father', name: 'father', desc: '', args: []);
  }

  /// `Address`
  String get address {
    return Intl.message('Address', name: 'address', desc: '', args: []);
  }

  /// `Title`
  String get title {
    return Intl.message('Title', name: 'title', desc: '', args: []);
  }

  /// `Message`
  String get message {
    return Intl.message('Message', name: 'message', desc: '', args: []);
  }

  /// `Send a new message`
  String get sendANewMessage {
    return Intl.message(
      'Send a new message',
      name: 'sendANewMessage',
      desc: '',
      args: [],
    );
  }

  /// `Send a new message to {name}`
  String sendANewMessageTo(Object name) {
    return Intl.message(
      'Send a new message to $name',
      name: 'sendANewMessageTo',
      desc: '',
      args: [name],
    );
  }

  /// `Are you sure to delete this event ?`
  String get areYouSureToDeleteThisEvent {
    return Intl.message(
      'Are you sure to delete this event ?',
      name: 'areYouSureToDeleteThisEvent',
      desc: '',
      args: [],
    );
  }

  /// `Edit Event`
  String get editEvent {
    return Intl.message('Edit Event', name: 'editEvent', desc: '', args: []);
  }

  /// `Teachers`
  String get teachers {
    return Intl.message('Teachers', name: 'teachers', desc: '', args: []);
  }

  /// `Reset your password`
  String get resetPassword {
    return Intl.message(
      'Reset your password',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Forget Password`
  String get forgetPassword {
    return Intl.message(
      'Forget Password',
      name: 'forgetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Enter new password`
  String get enterNewPassword {
    return Intl.message(
      'Enter new password',
      name: 'enterNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `Passwords should match`
  String get passwordsShouldMatch {
    return Intl.message(
      'Passwords should match',
      name: 'passwordsShouldMatch',
      desc: '',
      args: [],
    );
  }

  /// `Confirm new password`
  String get confirmNewPassword {
    return Intl.message(
      'Confirm new password',
      name: 'confirmNewPassword',
      desc: '',
      args: [],
    );
  }

  /// `User not Found`
  String get userNotFound {
    return Intl.message(
      'User not Found',
      name: 'userNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get add {
    return Intl.message('Add', name: 'add', desc: '', args: []);
  }

  /// `Supplies`
  String get supplies {
    return Intl.message('Supplies', name: 'supplies', desc: '', args: []);
  }

  /// `Food`
  String get food {
    return Intl.message('Food', name: 'food', desc: '', args: []);
  }

  /// `Toilet`
  String get toilet {
    return Intl.message('Toilet', name: 'toilet', desc: '', args: []);
  }

  /// `Sleep`
  String get sleep {
    return Intl.message('Sleep', name: 'sleep', desc: '', args: []);
  }

  /// `Breakfast`
  String get breakfast {
    return Intl.message('Breakfast', name: 'breakfast', desc: '', args: []);
  }

  /// `Snack`
  String get snack {
    return Intl.message('Snack', name: 'snack', desc: '', args: []);
  }

  /// `Lunch`
  String get lunch {
    return Intl.message('Lunch', name: 'lunch', desc: '', args: []);
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `More`
  String get more {
    return Intl.message('More', name: 'more', desc: '', args: []);
  }

  /// `Some`
  String get some {
    return Intl.message('Some', name: 'some', desc: '', args: []);
  }

  /// `None`
  String get none {
    return Intl.message('None', name: 'none', desc: '', args: []);
  }

  /// `Urine`
  String get urine {
    return Intl.message('Urine', name: 'urine', desc: '', args: []);
  }

  /// `Day`
  String get day {
    return Intl.message('Day', name: 'day', desc: '', args: []);
  }

  /// `Stool`
  String get stool {
    return Intl.message('Stool', name: 'stool', desc: '', args: []);
  }

  /// `Clothes`
  String get inClothes {
    return Intl.message('Clothes', name: 'inClothes', desc: '', args: []);
  }

  /// `Diaper`
  String get inTheDiaper {
    return Intl.message('Diaper', name: 'inTheDiaper', desc: '', args: []);
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `Paid successfully`
  String get paidSuccessfully {
    return Intl.message(
      'Paid successfully',
      name: 'paidSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Toilet`
  String get inTheToilet {
    return Intl.message('Toilet', name: 'inTheToilet', desc: '', args: []);
  }

  /// `Toilet Type`
  String get toiletType {
    return Intl.message('Toilet Type', name: 'toiletType', desc: '', args: []);
  }

  /// `Number of Students`
  String get numberOfStudents {
    return Intl.message(
      'Number of Students',
      name: 'numberOfStudents',
      desc: '',
      args: [],
    );
  }

  /// `Reminder sent successfully`
  String get reminderSentSuccessfully {
    return Intl.message(
      'Reminder sent successfully',
      name: 'reminderSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Subscription Remind`
  String get subscriptionRemind {
    return Intl.message(
      'Subscription Remind',
      name: 'subscriptionRemind',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to send subscription remind ?`
  String get areYouSureToSendSubscriptionRemind {
    return Intl.message(
      'Are you sure to send subscription remind ?',
      name: 'areYouSureToSendSubscriptionRemind',
      desc: '',
      args: [],
    );
  }

  /// `Fees`
  String get fees {
    return Intl.message('Fees', name: 'fees', desc: '', args: []);
  }

  /// `Today`
  String get today {
    return Intl.message('Today', name: 'today', desc: '', args: []);
  }

  /// `Monday`
  String get monday {
    return Intl.message('Monday', name: 'monday', desc: '', args: []);
  }

  /// `Tuesday`
  String get tuesday {
    return Intl.message('Tuesday', name: 'tuesday', desc: '', args: []);
  }

  /// `Wednesday`
  String get wednesday {
    return Intl.message('Wednesday', name: 'wednesday', desc: '', args: []);
  }

  /// `Thursday`
  String get thursday {
    return Intl.message('Thursday', name: 'thursday', desc: '', args: []);
  }

  /// `Friday`
  String get friday {
    return Intl.message('Friday', name: 'friday', desc: '', args: []);
  }

  /// `Saturday`
  String get saturday {
    return Intl.message('Saturday', name: 'saturday', desc: '', args: []);
  }

  /// `Sunday`
  String get sunday {
    return Intl.message('Sunday', name: 'sunday', desc: '', args: []);
  }

  /// `Meal Type`
  String get mealType {
    return Intl.message('Meal Type', name: 'mealType', desc: '', args: []);
  }

  /// `meal Amount`
  String get mealAmount {
    return Intl.message('meal Amount', name: 'mealAmount', desc: '', args: []);
  }

  /// `Class`
  String get tClass {
    return Intl.message('Class', name: 'tClass', desc: '', args: []);
  }

  /// `Gender`
  String get gender {
    return Intl.message('Gender', name: 'gender', desc: '', args: []);
  }

  /// `Assign Supply To Student`
  String get assignSupplyToStudent {
    return Intl.message(
      'Assign Supply To Student',
      name: 'assignSupplyToStudent',
      desc: '',
      args: [],
    );
  }

  /// `Send Supply To Student`
  String get sendSupplyToStudent {
    return Intl.message(
      'Send Supply To Student',
      name: 'sendSupplyToStudent',
      desc: '',
      args: [],
    );
  }

  /// `Send Supplies`
  String get sendSupplies {
    return Intl.message(
      'Send Supplies',
      name: 'sendSupplies',
      desc: '',
      args: [],
    );
  }

  /// `Assign Activity To Class`
  String get assignActivityToClass {
    return Intl.message(
      'Assign Activity To Class',
      name: 'assignActivityToClass',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Attendance Tracking`
  String get attendanceTracking {
    return Intl.message(
      'Attendance Tracking',
      name: 'attendanceTracking',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message('Reports', name: 'reports', desc: '', args: []);
  }

  /// `Update`
  String get update {
    return Intl.message('Update', name: 'update', desc: '', args: []);
  }

  /// `Activity chart`
  String get activityChart {
    return Intl.message(
      'Activity chart',
      name: 'activityChart',
      desc: '',
      args: [],
    );
  }

  /// `Month`
  String get month {
    return Intl.message('Month', name: 'month', desc: '', args: []);
  }

  /// `No activities found`
  String get noActivitiesFound {
    return Intl.message(
      'No activities found',
      name: 'noActivitiesFound',
      desc: '',
      args: [],
    );
  }

  /// `Max students reached, please contact support !`
  String get maxStudentsReachedPleaseContactSupport {
    return Intl.message(
      'Max students reached, please contact support !',
      name: 'maxStudentsReachedPleaseContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Change Language`
  String get changeLanguage {
    return Intl.message(
      'Change Language',
      name: 'changeLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Add Activity`
  String get addActivity {
    return Intl.message(
      'Add Activity',
      name: 'addActivity',
      desc: '',
      args: [],
    );
  }

  /// `Edit Activity`
  String get editActivity {
    return Intl.message(
      'Edit Activity',
      name: 'editActivity',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `Arabic`
  String get arabic {
    return Intl.message('Arabic', name: 'arabic', desc: '', args: []);
  }

  /// `UnAssign`
  String get unAssign {
    return Intl.message('UnAssign', name: 'unAssign', desc: '', args: []);
  }

  /// `Message sent successfully`
  String get messageSentSuccessfully {
    return Intl.message(
      'Message sent successfully',
      name: 'messageSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Change Password`
  String get changePassword {
    return Intl.message(
      'Change Password',
      name: 'changePassword',
      desc: '',
      args: [],
    );
  }

  /// `Validate your phone first please`
  String get validateYourPhoneFirstPlease {
    return Intl.message(
      'Validate your phone first please',
      name: 'validateYourPhoneFirstPlease',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match`
  String get passwordsDoNotMatch {
    return Intl.message(
      'Passwords do not match',
      name: 'passwordsDoNotMatch',
      desc: '',
      args: [],
    );
  }

  /// `Password Confirmation`
  String get passwordConfirmation {
    return Intl.message(
      'Password Confirmation',
      name: 'passwordConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `From time should be before to time`
  String get fromTimeShouldBeBeforeToTime {
    return Intl.message(
      'From time should be before to time',
      name: 'fromTimeShouldBeBeforeToTime',
      desc: '',
      args: [],
    );
  }

  /// `Enter valid nursery name`
  String get enterValidNurseryName {
    return Intl.message(
      'Enter valid nursery name',
      name: 'enterValidNurseryName',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid from & to time`
  String get pleaseEnterAValidFromToTime {
    return Intl.message(
      'Please enter a valid from & to time',
      name: 'pleaseEnterAValidFromToTime',
      desc: '',
      args: [],
    );
  }

  /// `Delete Account`
  String get deleteAccount {
    return Intl.message(
      'Delete Account',
      name: 'deleteAccount',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure to delete your account ?`
  String get areYouSureToDeleteYourAccount {
    return Intl.message(
      'Are you sure to delete your account ?',
      name: 'areYouSureToDeleteYourAccount',
      desc: '',
      args: [],
    );
  }

  /// `Have any questions?\nContact us`
  String get haveAnyQuestionsContactUs {
    return Intl.message(
      'Have any questions?\nContact us',
      name: 'haveAnyQuestionsContactUs',
      desc: '',
      args: [],
    );
  }

  /// `Choose activity assign type`
  String get chooseActivityAssignType {
    return Intl.message(
      'Choose activity assign type',
      name: 'chooseActivityAssignType',
      desc: '',
      args: [],
    );
  }

  /// `Weekly`
  String get weekly {
    return Intl.message('Weekly', name: 'weekly', desc: '', args: []);
  }

  /// `No persons`
  String get noPersons {
    return Intl.message('No persons', name: 'noPersons', desc: '', args: []);
  }

  /// `Class Activities`
  String get classActivities {
    return Intl.message(
      'Class Activities',
      name: 'classActivities',
      desc: '',
      args: [],
    );
  }

  /// `Search Student`
  String get searchStudent {
    return Intl.message(
      'Search Student',
      name: 'searchStudent',
      desc: '',
      args: [],
    );
  }

  /// `Enter pickup person`
  String get enterPickupPerson {
    return Intl.message(
      'Enter pickup person',
      name: 'enterPickupPerson',
      desc: '',
      args: [],
    );
  }

  /// `Pickups`
  String get pickups {
    return Intl.message('Pickups', name: 'pickups', desc: '', args: []);
  }

  /// `Persons`
  String get persons {
    return Intl.message('Persons', name: 'persons', desc: '', args: []);
  }

  /// `Add Note`
  String get addNote {
    return Intl.message('Add Note', name: 'addNote', desc: '', args: []);
  }

  /// `Note`
  String get note {
    return Intl.message('Note', name: 'note', desc: '', args: []);
  }

  /// `Single Activity`
  String get singleActivity {
    return Intl.message(
      'Single Activity',
      name: 'singleActivity',
      desc: '',
      args: [],
    );
  }

  /// `Pickup Person`
  String get pickupPerson {
    return Intl.message(
      'Pickup Person',
      name: 'pickupPerson',
      desc: '',
      args: [],
    );
  }

  /// `Weekly Activity`
  String get weeklyActivity {
    return Intl.message(
      'Weekly Activity',
      name: 'weeklyActivity',
      desc: '',
      args: [],
    );
  }

  /// `Add Pickup Person`
  String get addPickupPerson {
    return Intl.message(
      'Add Pickup Person',
      name: 'addPickupPerson',
      desc: '',
      args: [],
    );
  }

  /// `Add Media`
  String get addMedia {
    return Intl.message('Add Media', name: 'addMedia', desc: '', args: []);
  }

  /// `Media`
  String get media {
    return Intl.message('Media', name: 'media', desc: '', args: []);
  }

  /// `Saved successfully`
  String get savedSuccessfully {
    return Intl.message(
      'Saved successfully',
      name: 'savedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `No Media`
  String get noMedia {
    return Intl.message('No Media', name: 'noMedia', desc: '', args: []);
  }

  /// `Meals`
  String get meals {
    return Intl.message('Meals', name: 'meals', desc: '', args: []);
  }

  /// `Click to contact`
  String get clickToContact {
    return Intl.message(
      'Click to contact',
      name: 'clickToContact',
      desc: '',
      args: [],
    );
  }

  /// `Contact Support`
  String get contactSupport {
    return Intl.message(
      'Contact Support',
      name: 'contactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Warning`
  String get warning {
    return Intl.message('Warning', name: 'warning', desc: '', args: []);
  }

  /// `No Data`
  String get noData {
    return Intl.message('No Data', name: 'noData', desc: '', args: []);
  }

  /// `Total`
  String get total {
    return Intl.message('Total', name: 'total', desc: '', args: []);
  }

  /// `An update is required to continue using the app. Please update it now.`
  String get updateRequired {
    return Intl.message(
      'An update is required to continue using the app. Please update it now.',
      name: 'updateRequired',
      desc: '',
      args: [],
    );
  }

  /// `Select Classes`
  String get selectClasses {
    return Intl.message(
      'Select Classes',
      name: 'selectClasses',
      desc: '',
      args: [],
    );
  }

  /// `No results found`
  String get noResultsFound {
    return Intl.message(
      'No results found',
      name: 'noResultsFound',
      desc: '',
      args: [],
    );
  }

  /// `Search Classes`
  String get searchClasses {
    return Intl.message(
      'Search Classes',
      name: 'searchClasses',
      desc: '',
      args: [],
    );
  }

  /// `Please select a class`
  String get selectClass {
    return Intl.message(
      'Please select a class',
      name: 'selectClass',
      desc: '',
      args: [],
    );
  }

  /// `Paid`
  String get paid {
    return Intl.message('Paid', name: 'paid', desc: '', args: []);
  }

  /// `Unpaid`
  String get unpaid {
    return Intl.message('Unpaid', name: 'unpaid', desc: '', args: []);
  }

  /// `Are you sure to make this subscription paid ?`
  String get areYouSureToMakeThisSubscriptionPaid {
    return Intl.message(
      'Are you sure to make this subscription paid ?',
      name: 'areYouSureToMakeThisSubscriptionPaid',
      desc: '',
      args: [],
    );
  }

  /// `You are parent, please register on parent application !`
  String get youAreParentPleaseRegisterOnParentApplication {
    return Intl.message(
      'You are parent, please register on parent application !',
      name: 'youAreParentPleaseRegisterOnParentApplication',
      desc: '',
      args: [],
    );
  }

  /// `You are parent, please login on parent application !`
  String get youAreParentPleaseLoginOnParentApplication {
    return Intl.message(
      'You are parent, please login on parent application !',
      name: 'youAreParentPleaseLoginOnParentApplication',
      desc: '',
      args: [],
    );
  }

  /// `Subscription expired, please contact support !`
  String get subscriptionExpiredPleaseContactSupport {
    return Intl.message(
      'Subscription expired, please contact support !',
      name: 'subscriptionExpiredPleaseContactSupport',
      desc: '',
      args: [],
    );
  }

  /// `Admin already registered`
  String get adminAlreadyRegistered {
    return Intl.message(
      'Admin already registered',
      name: 'adminAlreadyRegistered',
      desc: '',
      args: [],
    );
  }

  /// `Amount`
  String get amount {
    return Intl.message('Amount', name: 'amount', desc: '', args: []);
  }

  /// `Mother or Parent already registered`
  String get motherOrParentAlreadyRegistered {
    return Intl.message(
      'Mother or Parent already registered',
      name: 'motherOrParentAlreadyRegistered',
      desc: '',
      args: [],
    );
  }

  /// `If you continue this will affect parents app and they can see all children under their number`
  String
  get IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber {
    return Intl.message(
      'If you continue this will affect parents app and they can see all children under their number',
      name:
          'IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber',
      desc: '',
      args: [],
    );
  }

  /// `Supply`
  String get supply {
    return Intl.message('Supply', name: 'supply', desc: '', args: []);
  }

  /// `Activity`
  String get activity {
    return Intl.message('Activity', name: 'activity', desc: '', args: []);
  }

  /// `No history for this date`
  String get noHistoryForThisDate {
    return Intl.message(
      'No history for this date',
      name: 'noHistoryForThisDate',
      desc: '',
      args: [],
    );
  }

  /// `Student`
  String get student {
    return Intl.message('Student', name: 'student', desc: '', args: []);
  }

  /// `Clear`
  String get clear {
    return Intl.message('Clear', name: 'clear', desc: '', args: []);
  }

  /// `Announcements`
  String get announcements {
    return Intl.message(
      'Announcements',
      name: 'announcements',
      desc: '',
      args: [],
    );
  }

  /// `Add Announcement`
  String get addAnnouncement {
    return Intl.message(
      'Add Announcement',
      name: 'addAnnouncement',
      desc: '',
      args: [],
    );
  }

  /// `Edit Announcement`
  String get editAnnouncement {
    return Intl.message(
      'Edit Announcement',
      name: 'editAnnouncement',
      desc: '',
      args: [],
    );
  }

  /// `Target`
  String get target {
    return Intl.message('Target', name: 'target', desc: '', args: []);
  }

  /// `Select Target`
  String get selectTarget {
    return Intl.message(
      'Select Target',
      name: 'selectTarget',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get resend {
    return Intl.message('Resend', name: 'resend', desc: '', args: []);
  }

  /// `Delete Announcement`
  String get deleteAnnouncement {
    return Intl.message(
      'Delete Announcement',
      name: 'deleteAnnouncement',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this announcement?`
  String get areYouSureYouWantToDeleteThisAnnouncement {
    return Intl.message(
      'Are you sure you want to delete this announcement?',
      name: 'areYouSureYouWantToDeleteThisAnnouncement',
      desc: '',
      args: [],
    );
  }

  /// `By`
  String get by {
    return Intl.message('By', name: 'by', desc: '', args: []);
  }

  /// `Send`
  String get send {
    return Intl.message('Send', name: 'send', desc: '', args: []);
  }

  /// `This field is required`
  String get thisFieldIsRequired {
    return Intl.message(
      'This field is required',
      name: 'thisFieldIsRequired',
      desc: '',
      args: [],
    );
  }

  /// `Plan`
  String get plan {
    return Intl.message('Plan', name: 'plan', desc: '', args: []);
  }

  /// `Plans`
  String get plans {
    return Intl.message('Plans', name: 'plans', desc: '', args: []);
  }

  /// `Add Plan`
  String get addPlan {
    return Intl.message('Add Plan', name: 'addPlan', desc: '', args: []);
  }

  /// `Edit Plan`
  String get editPlan {
    return Intl.message('Edit Plan', name: 'editPlan', desc: '', args: []);
  }

  /// `Delete Plan`
  String get deletePlan {
    return Intl.message('Delete Plan', name: 'deletePlan', desc: '', args: []);
  }

  /// `Are you sure you want to delete this plan?`
  String get areYouSureYouWantToDeleteThisPlan {
    return Intl.message(
      'Are you sure you want to delete this plan?',
      name: 'areYouSureYouWantToDeleteThisPlan',
      desc: '',
      args: [],
    );
  }

  /// `No plans for this month`
  String get noPlansForThisMonth {
    return Intl.message(
      'No plans for this month',
      name: 'noPlansForThisMonth',
      desc: '',
      args: [],
    );
  }

  /// `Sections`
  String get sections {
    return Intl.message('Sections', name: 'sections', desc: '', args: []);
  }

  /// `Section`
  String get section {
    return Intl.message('Section', name: 'section', desc: '', args: []);
  }

  /// `Add New Section`
  String get addNewSection {
    return Intl.message(
      'Add New Section',
      name: 'addNewSection',
      desc: '',
      args: [],
    );
  }

  /// `Remove Section`
  String get removeSection {
    return Intl.message(
      'Remove Section',
      name: 'removeSection',
      desc: '',
      args: [],
    );
  }

  /// `Section Title`
  String get sectionTitle {
    return Intl.message(
      'Section Title',
      name: 'sectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Section Description`
  String get sectionDescription {
    return Intl.message(
      'Section Description',
      name: 'sectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter section title`
  String get enterSectionTitle {
    return Intl.message(
      'Enter section title',
      name: 'enterSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Enter section description`
  String get enterSectionDescription {
    return Intl.message(
      'Enter section description',
      name: 'enterSectionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Please add at least one section`
  String get pleaseAddAtLeastOneSection {
    return Intl.message(
      'Please add at least one section',
      name: 'pleaseAddAtLeastOneSection',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
