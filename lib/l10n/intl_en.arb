{"speakWithConfidence": "Speak with confidence", "getTalkingFrom": "Every day is a journey. \nSign in to join us.", "signUp": "Sign Up", "alreadyHaveAnAccount": "Already have an account?", "dontHaveAnAccount": "You don’t have account?", "noQuestions": "No questions", "question": "Question", "exams": "<PERSON><PERSON>", "addNewQuestion": "Add New Question", "sessions": "Sessions", "areYouSureToDeleteThisQuestion": "Are you sure to delete this question ?", "youCannotDeleteThisQuestionBecauseitsHasStudentResults": "You cannot delete this question because it's has student results", "signIn": "Sign In", "SignupAsa": "Sign up as a", "administrator": "Administrator", "history": "History", "results": "Results", "teacher": "Teacher", "searchQuestion": "Search Question", "phoneNumber": "Phone Number", "password": "Password", "enter": "Enter", "allStudents": "All Students", "adminSignUp": "Administrator Sign up", "teacherSignUp": "Teacher Sign up", "nurseryLogo": "Nursery Logo", "nurseryName": "Nursery Name", "email": "Email", "iHaveReadThe": "I have read the", "subscriptions": "Subscriptions", "activeStudents": "Active Students", "privacyPolicy": "Privacy Policy", "finishLetsStart": "Finish, let’s start", "uploadLogo": "Upload logo", "setupYourClasses": "Setup your classes", "addNewClass": "Add New Class", "editClass": "Edit Class", "SkipForNow": "Skip for now", "goodMorning": "Good Morning!", "goodAfternoon": "Good Afternoon!", "classes": "Classes", "className": "Class Name", "classDescription": "Class Description", "team": "Team", "students": "Students", "activities": "Activities", "staff": "Staff", "addStudents": "Add Students", "addNewStudents": "Add New Students", "skipForNow": "Skip for Now", "addNurseryTeam": "Add Nursery Team", "addNurseryTeamMember": "Add New Team Member", "skip": "<PERSON><PERSON>", "letsStart": "Let’s Start", "nurseryActivities": "Add a Nursery Activities", "addNurseryActivities": "Add New Activity ", "maxUploadFilesIsOnly4": "Max upload images is only 4", "maxUploadFileSizeIsOnly5MB": "Max upload image size is only 5 MB", "congratulations": "Congratulations", "letsDoAGreatJob": "Let’s do a great job", "dashboard": "Dashboard", "messages": "Messages", "home": "Home", "attendeesOfToday": "Attendees of Today", "Of": "Of", "currentActivity": "Current Activity", "attendance": "Attendance", "financial": "Financial", "emergency": "Emergency", "events": "Events", "members": "Members", "eventThisMonth": "Event this month", "createNewClass": "Create new class", "back": "Back", "myClasses": "My Classes", "myClass": "My Class", "teacherInfo": "Teacher info", "dailySchedule": "Daily Schedule", "pickImage": "Pick Image", "save": "Save", "createANewClass": "Create a new class", "createANewSupply": "Add a new Supply", "addANewStaffMember": "Add a new staff member", "teacherName": "Teacher Name", "supplyName": "supply Name", "description": "Description", "pleasePickAnImage": "Please pick an image", "studentName": "Student Name", "matherPhoneNumber": "Mather Phone number", "homeAddress": "Home Address", "addParentsPhoneNumber": "Add Parents Phone number", "birthDate": "Birth Date", "activityName": "Activity Name", "activityDescription": "Activity Description", "next": "Next", "motherPhoneNumber": "Mother Phone number", "parentPhoneNumber": "Parent Phone number", "attendanceChart": "Attendance chart", "studentAndClass": "Student & Class", "selectPeriod": "Select period", "attended": "attended", "absent": "absent", "incomeChart": "Income chart", "activitiesCompleted": "Activities completed", "billsChart": "Bills chart", "invoicesChart": "Income chart", "currentMonth": "Current month", "assignToClass": "Assign to class", "assign": "Assign", "assigned": "Assigned", "invoices": "Income", "bills": "Bills", "addNewBill": "Add New Bill", "from": "From", "to": "To", "addNewInvoice": "Add New Income", "edit": "Edit", "delete": "Delete", "date": "Date", "billName": "<PERSON>", "billAmount": "<PERSON>", "confirm": "Confirm", "confirmation": "Confirmation", "cancel": "Cancel", "areYouSureToDeleteThisBill": "Are you sure to delete this bill ?", "areYouSureToDeleteThisInvoice": "Are you sure to delete this Income ?", "areYouSureToDeleteThisClass": "Are you sure to delete this class ?", "areYouSureToDeleteThisSupply": "Are you sure to delete this supply ?", "areYouSureToDeleteThisTeacher": "Are you sure to delete this teacher ?", "areYouSureToDeleteThisStudent": "Are you sure to delete this student ?", "areYouSureToDeleteThisActivity": "Are you sure to delete this activity ?", "invoiceAmount": "Income Amount", "invoiceName": "Income Name", "deletedSuccessfully": "deleted successfully", "editSuccessfully": "Edit successfully", "editTeacher": "Edit Teacher", "addedSuccessfully": "Added successfully", "didNotGetCode": "Didn’t get the code?", "resendCode": "Resend Code", "completeVerification": "Complete Verification", "errorOccurred": "Error occurred", "verificationCodeIsWrong": "Verification code is wrong", "enterOtp": "Enter OTP", "sentVerificationCode": "We sent a verification code to", "submit": "Submit", "verify": "verify", "enterValidPhoneNumber": "Enter valid phone number", "enterPhoneNumberFirst": "First enter your phone number", "verificationSuccessful": "Verification successful", "pleaseAcceptTerms": "Please accept terms", "pleaseVerifyPhone": "Please verify phone", "noEvents": "No events", "noBills": "No Bills", "noInvoices": "No Income", "noClasses": "No Classes", "noNotifications": "No Notifications", "noTeachers": "No Teachers", "noStudents": "No Students", "noActivities": "No Activities", "noSupplies": "No Supplies", "active": "Active", "addNewEvent": "Add New Event", "eventName": "Event Name", "eventType": "Event Type", "mother": "Mother", "father": "Father", "address": "Address", "title": "Title", "message": "Message", "sendANewMessage": "Send a new message", "sendANewMessageTo": "Send a new message to {name}", "areYouSureToDeleteThisEvent": "Are you sure to delete this event ?", "editEvent": "Edit Event", "teachers": "Teachers", "resetPassword": "Reset your password", "forgetPassword": "Forget Password", "enterNewPassword": "Enter new password", "passwordsShouldMatch": "Passwords should match", "confirmNewPassword": "Confirm new password", "userNotFound": "User not Found", "add": "Add", "supplies": "Supplies", "food": "Food", "toilet": "<PERSON><PERSON><PERSON>", "sleep": "Sleep", "breakfast": "Breakfast", "snack": "Snack", "lunch": "Lunch", "all": "All", "more": "More", "some": "Some", "none": "None", "urine": "<PERSON><PERSON>", "day": "Day", "stool": "Stool", "inClothes": "<PERSON><PERSON><PERSON>", "inTheDiaper": "Diaper", "notifications": "Notifications", "paidSuccessfully": "<PERSON><PERSON> successfully", "inTheToilet": "<PERSON><PERSON><PERSON>", "toiletType": "Toilet Type", "numberOfStudents": "Number of Students", "reminderSentSuccessfully": "<PERSON><PERSON><PERSON> sent successfully", "subscriptionRemind": "Subscription Remind", "areYouSureToSendSubscriptionRemind": "Are you sure to send subscription remind ?", "fees": "Fees", "today": "Today", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mealType": "Meal Type", "mealAmount": "meal Amount", "tClass": "Class", "gender": "Gender", "assignSupplyToStudent": "Assign Supply To Student", "sendSupplyToStudent": "Send Supply To Student", "sendSupplies": "Send Supplies", "assignActivityToClass": "Assign Activity To Class", "logout": "Logout", "attendanceTracking": "Attendance Tracking", "reports": "Reports", "update": "Update", "activityChart": "Activity chart", "month": "Month", "noActivitiesFound": "No activities found", "maxStudentsReachedPleaseContactSupport": "Max students reached, please contact support !", "changeLanguage": "Change Language", "addActivity": "Add Activity", "editActivity": "Edit Activity", "english": "English", "arabic": "Arabic", "unAssign": "UnAssign", "messageSentSuccessfully": "Message sent successfully", "profile": "Profile", "changePassword": "Change Password", "validateYourPhoneFirstPlease": "Validate your phone first please", "passwordsDoNotMatch": "Passwords do not match", "passwordConfirmation": "Password Confirmation", "name": "Name", "search": "Search", "fromTimeShouldBeBeforeToTime": "From time should be before to time", "enterValidNurseryName": "Enter valid nursery name", "pleaseEnterAValidFromToTime": "Please enter a valid from & to time", "deleteAccount": "Delete Account", "areYouSureToDeleteYourAccount": "Are you sure to delete your account ?", "haveAnyQuestionsContactUs": "Have any questions?\nContact us", "chooseActivityAssignType": "Choose activity assign type", "weekly": "Weekly", "noPersons": "No persons", "classActivities": "Class Activities", "searchStudent": "Search Student", "enterPickupPerson": "Enter pickup person", "pickups": "Pickups", "persons": "Persons", "addNote": "Add Note", "note": "Note", "singleActivity": "Single Activity", "pickupPerson": "Pickup Person", "weeklyActivity": "Weekly Activity", "addPickupPerson": "Add Pickup Person", "addMedia": "Add Media", "media": "Media", "savedSuccessfully": "Saved successfully", "noMedia": "No Media", "meals": "Meals", "clickToContact": "Click to contact", "contactSupport": "Contact Support", "warning": "Warning", "noData": "No Data", "total": "Total", "updateRequired": "An update is required to continue using the app. Please update it now.", "selectClasses": "Select Classes", "noResultsFound": "No results found", "searchClasses": "Search Classes", "selectClass": "Select Class", "paid": "Paid", "unpaid": "Unpaid", "areYouSureToMakeThisSubscriptionPaid": "Are you sure to make this subscription paid ?", "youAreParentPleaseRegisterOnParentApplication": "You are parent, please register on parent application !", "youAreParentPleaseLoginOnParentApplication": "You are parent, please login on parent application !", "subscriptionExpiredPleaseContactSupport": "Subscription expired, please contact support !", "adminAlreadyRegistered": "Admin already registered", "amount": "Amount", "motherOrParentAlreadyRegistered": "Mother or Parent already registered", "IfYouContinueThisWillAffectParentsAppAndTheyCanSeeAllChildrenUnderTheirNumber": "If you continue this will affect parents app and they can see all children under their number", "supply": "Supply", "activity": "Activity", "noHistoryForThisDate": "No history for this date", "student": "Student", "announcements": "Announcements", "addAnnouncement": "Add Announcement", "editAnnouncement": "Edit Announcement", "target": "Target", "selectTarget": "Select Target", "resend": "Resend", "deleteAnnouncement": "Delete Announcement", "areYouSureYouWantToDeleteThisAnnouncement": "Are you sure you want to delete this announcement?", "by": "By", "send": "Send", "thisFieldIsRequired": "This field is required", "plans": "Plans", "addPlan": "Add Plan", "editPlan": "Edit Plan", "deletePlan": "Delete Plan", "areYouSureYouWantToDeleteThisPlan": "Are you sure you want to delete this plan?", "noPlansForThisMonth": "No plans for this month", "sections": "Sections", "section": "Section", "addNewSection": "Add New Section", "removeSection": "Remove Section", "sectionTitle": "Section Title", "sectionDescription": "Section Description", "enterSectionTitle": "Enter section title", "enterSectionDescription": "Enter section description"}