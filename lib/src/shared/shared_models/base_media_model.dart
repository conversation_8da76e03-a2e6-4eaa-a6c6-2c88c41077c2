class BaseMediaModel {
  final int? id;
  final String? publicId;
  final String? url;

  const BaseMediaModel({this.id, required this.url, this.publicId});

  factory BaseMediaModel.fromJson(Map<String, dynamic>? json, {int? id}) {
    if (json == null) {
      return BaseMediaModel.empty();
    }

    final publicId = json['provider_metadata'] != null
        ? json['provider_metadata']['public_id']
        : '';

    return BaseMediaModel(
      id: id ?? json['id'],
      publicId: publicId,
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      "public_id": publicId,
      "url": url,
    };
  }

  factory BaseMediaModel.empty() => const BaseMediaModel(url: '');
}
