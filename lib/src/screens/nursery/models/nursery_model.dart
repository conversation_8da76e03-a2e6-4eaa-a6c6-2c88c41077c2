import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';

class NurseryModel extends UserModel {
  final int maxStudents;
  final num fees;
  final DateTime? endDate;

  const NurseryModel({
    super.id,
    super.name,
    super.image,
    this.maxStudents = AppConsts.maxStudents,
    this.fees = 0,
    this.endDate,
  });

  factory NurseryModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes != null &&
            attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(
            attributes[ApiStrings.logo][ApiStrings.data][ApiStrings.attributes])
        : null;

    return NurseryModel(
      id: json[ApiStrings.id],
      name: attributes != null ? (attributes[ApiStrings.name] ?? '') : '',
      maxStudents: attributes[ApiStrings.maxStudents] != null
          ? (attributes[ApiStrings.maxStudents])
          : AppConsts.maxStudents,
      fees: attributes[ApiStrings.fees] ?? 0,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
    );
  }

  factory NurseryModel.fromJson(Map<String, dynamic> attributes) {
    final logo = attributes[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.logo])
        : null;

    return NurseryModel(
      id: attributes[ApiStrings.id],
      maxStudents: attributes[ApiStrings.maxStudents] ?? AppConsts.maxStudents,
      name: attributes.isNotEmpty ? (attributes[ApiStrings.name] ?? '') : '',
      fees: attributes[ApiStrings.fees] ?? 0,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
    );
  }

  // to json
  Map<String, dynamic> toDataJson() {
    return {
      ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.logo: image?.toJson(),
      ApiStrings.maxStudents: maxStudents,
      ApiStrings.fees: fees,
    };
  }
}
