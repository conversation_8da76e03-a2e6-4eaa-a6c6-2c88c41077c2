import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/teacher_activity_screen.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/assign_activity_dialog/assign_activity_dialog.dart';
import 'package:connectify_app/src/screens/Activities/view/teacher_activty/teacher_activty_add_activity/view/widgets/teacher_activity_card.dart';
import 'package:connectify_app/src/screens/class/view/classes_details_screen/widgets/date_attendance_filter_widget.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../shared/consts/app_constants.dart';
import '../../../../../controllers/teacher_activities_controller.dart';

class TeacherActivityAddList extends HookConsumerWidget {
  const TeacherActivityAddList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allDatesOfYear = generateAllDatesOfYear();
    final today = DateTime.now();
    final initialIndex = allDatesOfYear
        .indexWhere((date) => date['date'] == today.formatDateToString);
    final selectedIndex = useState(initialIndex >= 0 ? initialIndex : 0);
    final deletedActivityIds = useState<Set<int>>({});
    final isLoading = useState(false);

    final params = (
      context,
      allDatesOfYear[selectedIndex.value]['date'].toString(),
      allDatesOfYear[selectedIndex.value]['day'].toString()
    );

    final getTeacherActivityCtrl =
        ref.watch(getTeacherActivitiesByDateProvider(params));

    final teacherActivity = getTeacherActivityCtrl.when(
      loading: () {
        isLoading.value = true;
        return <TeacherActivityModel>[];
      },
      error: (error, stack) {
        context.showBarMessage(context.tr.errorOccurred);
        return <TeacherActivityModel>[];
      },
      data: (teacherActivities) {
        isLoading.value = false;
        return teacherActivities;
      },
    );

    final filterList = useState<List<TeacherActivityModel>>(teacherActivity
        .where((element) =>
            (element.day == allDatesOfYear[selectedIndex.value]['day'] ||
                (!element.isWeekly &&
                    element.date ==
                        allDatesOfYear[selectedIndex.value]['date'])) &&
            !deletedActivityIds.value.contains(element.id))
        .toList());

    final activityController =
        ref.watch(teacherActivityChangeNotifierProvider(context));

    useEffect(() {
      filterList.value = teacherActivity
          .where((element) =>
              (element.isWeekly &&
                  element.day == allDatesOfYear[selectedIndex.value]['day']) ||
              (!element.isWeekly &&
                      element.date ==
                          allDatesOfYear[selectedIndex.value]['date']) &&
                  !deletedActivityIds.value.contains(element.id))
          .toList();

      filterList.value.sort((a, b) {
        final startTimeA =
            a.startTime.split(':').map((e) => e.padLeft(2, '0')).join(':');
        final startTimeB =
            b.startTime.split(':').map((e) => e.padLeft(2, '0')).join(':');

        log('Formatted startTimeA: $startTimeA');
        log('Formatted startTimeB: $startTimeB');

        return startTimeA.compareTo(startTimeB);
      });
      // filterList.value.sort((a, b) {
      //   final timeFormat = DateFormat('HH:mm');
      //   return timeFormat
      //           .tryParse(a.startTime)
      //           ?.compareTo(timeFormat.parse(b.startTime)) ??
      //       0;
      // });

      return () {};
    }, [selectedIndex.value, teacherActivity, deletedActivityIds.value]);

    final teacherActivityList = filterList.value
        .where((element) => !deletedActivityIds.value.contains(element.id))
        .toList();

    final controllers = List.generate(teacherActivityList.length, (index) {
      final teacherActivity = teacherActivityList[index];
      final noteForDate = teacherActivity.notes.firstWhereOrNull(
        (note) => note.date == allDatesOfYear[selectedIndex.value]['date'],
      );

      return {
        ApiStrings.from: ValueNotifier(teacherActivity.startTime),
        ApiStrings.to: ValueNotifier(teacherActivity.endTime),
        ApiStrings.note: ValueNotifier(noteForDate?.note ?? ''),
        ApiStrings.media:
            ValueNotifier(noteForDate?.media ?? <BaseMediaModel>[]),
        ApiStrings.isUpdated: ValueNotifier(false),
      };
    });

    Future<void> editTeacherActivity() async {
      await Future.forEach(teacherActivityList, (element) async {
        final isUpdated = (controllers[teacherActivityList.indexOf(element)]
                [ApiStrings.isUpdated]! as ValueNotifier)
            .value;

        if (!isUpdated) return;

        final noteValue = (controllers[teacherActivityList.indexOf(element)]
                [ApiStrings.note]! as ValueNotifier)
            .value;
        final mediaValue = (controllers[teacherActivityList.indexOf(element)]
                [ApiStrings.media]! as ValueNotifier)
            .value;

        await activityController.editActivity(
          teacherActivity: TeacherActivityModel(
              isWeekly: element.isWeekly,
              startTime: (controllers[teacherActivityList.indexOf(element)]
                      [ApiStrings.from]! as ValueNotifier)
                  .value,
              endTime: (controllers[teacherActivityList.indexOf(element)]
                      [ApiStrings.to]! as ValueNotifier)
                  .value,
              notes: [
                ...element.notes.where((element) =>
                    element.note.isNotEmpty &&
                    element.date !=
                        allDatesOfYear[selectedIndex.value]['date']),
                if ((noteValue != '.' && noteValue.isNotEmpty) ||
                    mediaValue.isNotEmpty)
                  ActivityNoteModel(
                    date: allDatesOfYear[selectedIndex.value]['date']!,
                    //? Check if noteValue is '.' then set it to empty string because it's for add button only
                    note: noteValue != '.' && noteValue.isNotEmpty
                        ? noteValue
                        : '',
                    media: mediaValue,
                  )
              ],
              day: allDatesOfYear[selectedIndex.value]['day']!),
          id: element.id!,
        );
      });

      context.back();
      context.to(const TeacherActivitiesScreen());
      context.showBarMessage(context.tr.editSuccessfully);
    }

    // edit one activity
    Future<void> editOneActivity(TeacherActivityModel teacherActivity) async {
      final noteValue =
          (controllers[teacherActivityList.indexOf(teacherActivity)]
                  [ApiStrings.note]! as ValueNotifier)
              .value;
      final mediaValue =
          (controllers[teacherActivityList.indexOf(teacherActivity)]
                  [ApiStrings.media]! as ValueNotifier)
              .value;

      await activityController.editActivity(
        teacherActivity: TeacherActivityModel(
            isWeekly: teacherActivity.isWeekly,
            startTime:
                (controllers[teacherActivityList.indexOf(teacherActivity)]
                        [ApiStrings.from]! as ValueNotifier)
                    .value,
            endTime: (controllers[teacherActivityList.indexOf(teacherActivity)]
                    [ApiStrings.to]! as ValueNotifier)
                .value,
            notes: [
              ...teacherActivity.notes.where((element) =>
                  element.note.isNotEmpty &&
                  element.date != allDatesOfYear[selectedIndex.value]['date']),
              if ((noteValue != '.' && noteValue.isNotEmpty) ||
                  mediaValue.isNotEmpty)
                ActivityNoteModel(
                  date: allDatesOfYear[selectedIndex.value]['date']!,
                  //? Check if noteValue is '.' then set it to empty string because it's for add button only
                  note:
                      noteValue != '.' && noteValue.isNotEmpty ? noteValue : '',
                  media: mediaValue,
                )
            ],
            day: allDatesOfYear[selectedIndex.value]['day']!),
        id: teacherActivity.id!,
      );

      context.showBarMessage(context.tr.addedSuccessfully);
    }

    final isEmptyList = teacherActivityList.isEmpty;

    return Column(
      children: [
        Expanded(
          child: Column(
            children: [
              const AssignActivityDialog(),
              context.mediumGap,
              DateAttendanceFilterWidget(
                selectedDay: allDatesOfYear[selectedIndex.value]['day']!,
                date: allDatesOfYear[selectedIndex.value]['date']!,
                onNext: () {
                  if (selectedIndex.value < allDatesOfYear.length - 1) {
                    selectedIndex.value++;
                  }
                },
                onPrevious: () {
                  if (selectedIndex.value > 0) {
                    selectedIndex.value--;
                  }
                },
              ),
              context.mediumGap,
              if (isLoading.value) ...[
                const Center(child: LoadingWidget()),
              ] else if (isEmptyList) ...[
                Column(
                  children: [
                    context.xxLargeGap,
                    Text(
                      context.tr.noActivities,
                      style: context.headLine,
                    ),
                  ],
                ),
              ] else ...[
                Expanded(
                  child: Builder(builder: (context) {
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: teacherActivityList.length,
                      itemBuilder: (context, index) => WidgetAnimator(
                        delay: Duration(
                            milliseconds: AppConsts.animatedDuration * index),
                        child: TeacherActivityCardWidget(
                          controllers: controllers[index],
                          teacherActivity: teacherActivityList[index],
                          teacherActivityList: filterList,
                          deletedActivityIds: deletedActivityIds,
                          selectedDate: allDatesOfYear[selectedIndex.value]
                              ['date']!,
                          onSavedMedia: () {
                            editOneActivity(teacherActivityList[index]);
                          },
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ],
          ),
        ),
        if (!isEmptyList)
          Button(
            isLoading: activityController.isLoading,
            loadingWidget: const Center(child: LinearProgressIndicator()),
            label: context.tr.update,
            onPressed: editTeacherActivity,
          ).sized(height: 40.h),
      ],
    );
  }

  List<Map<String, String>> generateAllDatesOfYear() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    final dates = <Map<String, String>>[];

    for (var date = startOfYear;
        date.isBefore(endOfYear.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      dates.add({
        'day': DateFormat('EEEE', 'en').format(date),
        'date': date.formatDateToString,
      });
    }
    return dates;
  }
}

// class TeacherActivityAddList extends HookConsumerWidget {
//   const TeacherActivityAddList({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final getTeacherActivityCtrl =
//         ref.watch(getTeacherActivitiesDataProvider(context));
//     final teacherActivityCtrl =
//         ref.watch(teacherActivityProviderController(context));
//
//     final activityCtrl =
//         ref.watch(teacherActivityChangeNotifierProvider(context));
//
//     final deletedActivityIds = useState<Set<int>>({});
//
//     final isLoading = useState(false);
//
//     final teacherActivity = getTeacherActivityCtrl.when(loading: () {
//       isLoading.value = true;
//       return <TeacherActivityModel>[];
//     }, error: (error, stack) {
//       context.showBarMessage(context.tr.errorOccurred);
//       return <TeacherActivityModel>[];
//     }, data: (teacherActivities) {
//       isLoading.value = false;
//       return teacherActivities;
//     });
//
//     return HookBuilder(builder: (context) {
//       final dayNumber = AppDateTime.dayNumber;
//       final todayIndex = dayNumber - 1;
//       final selectedIndex = useState(todayIndex);
//
//       final filterList = useState<List<TeacherActivityModel>>(teacherActivity
//           .where((element) =>
//               element.day == weekDays[todayIndex] &&
//               !deletedActivityIds.value.contains(element.id))
//           .toList());
//
//       useEffect(
//         () {
//           filterList.value = teacherActivity
//               .where((element) =>
//                   element.day == weekDays[selectedIndex.value] &&
//                   !deletedActivityIds.value.contains(element.id))
//               .toList();
//
//           filterList.value.sort((a, b) {
//             final timeFormat = DateFormat('HH:mm');
//             final timeA = timeFormat.parse(a.startTime);
//             final timeB = timeFormat.parse(b.startTime);
//             return timeA.compareTo(timeB);
//           });
//
//           return () {};
//         },
//         [selectedIndex.value, teacherActivity, deletedActivityIds.value],
//       );
//
//       final controllers = List.generate(filterList.value.length, (index) {
//         final teacherActivity = filterList.value[index];
//
//         return {
//           ApiStrings.from: useTextEditingController(
//             text: teacherActivity.startTime,
//           ),
//           ApiStrings.to: useTextEditingController(
//             text: teacherActivity.endTime,
//           ),
//         };
//       });
//
//       Future<void> editTeacherActivity() async {
//         await Future.forEach(filterList.value, (element) async {
//           await activityCtrl.editActivity(
//               teacherActivity: TeacherActivityModel(
//                   startTime: controllers[filterList.value.indexOf(element)]
//                           [ApiStrings.from]!
//                       .text,
//                   endTime: controllers[filterList.value.indexOf(element)]
//                           [ApiStrings.to]!
//                       .text,
//                   day: weekDays[selectedIndex.value]),
//               id: element.id!);
//         });
//
//         context.back();
//         context.to(const TeacherActivitiesScreen());
//         context.showBarMessage(context.tr.editSuccessfully);
//       }
//
//       final isEmptyList = filterList.value.isEmpty;
//
//
//       if (isLoading.value) {
//         return const Center(child: LoadingWidget());
//       }
//
//       return Column(
//         children: [
//           Expanded(
//             child: Column(
//               children: [
//                 const AssignActivityDialog(),
//                 context.mediumGap,
//                 DateAttendanceFilterWidget(
//                   selectedDay: weekDays[selectedIndex.value],
//                   onNext: () =>
//                       teacherActivityCtrl.onNext(selectedIndex: selectedIndex),
//                   onPrevious: () =>
//                       teacherActivityCtrl.onPrev(selectedIndex: selectedIndex),
//                   date: dateByDay,
//                 ),
//                 context.mediumGap,
//                 if (isEmptyList) ...[
//                   Column(
//                     children: [
//                       context.xxLargeGap,
//                       Text(
//                         context.tr.noActivities,
//                         style: context.headLine,
//                       ),
//                     ],
//                   ),
//                 ] else ...[
//                   Expanded(
//                     child: ListView.builder(
//                         shrinkWrap: true,
//                         itemBuilder: (context, index) => WidgetAnimator(
//                             delay: Duration(
//                                 milliseconds:
//                                     AppConsts.animatedDuration * index),
//                             child: TeacherActivityCard(
//                               controllers: controllers[index],
//                               teacherActivity: filterList.value[index],
//                               teacherActivityList: filterList,
//                               deletedActivityIds: deletedActivityIds,
//                             )),
//                         itemCount: filterList.value.length),
//                   ),
//                 ],
//               ],
//             ),
//           ),
//           if (!isEmptyList)
//             Button(
//               isLoading: activityCtrl.isLoading,
//               loadingWidget: const Center(child: LinearProgressIndicator()),
//               label: context.tr.update,
//               onPressed: editTeacherActivity,
//             ).sized(height: 40.h),
//         ],
//       );
//     });
//   }
// }
