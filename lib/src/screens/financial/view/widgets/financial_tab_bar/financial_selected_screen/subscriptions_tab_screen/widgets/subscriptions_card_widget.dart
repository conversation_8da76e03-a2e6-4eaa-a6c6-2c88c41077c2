import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/financial/view/financial_screen.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

class SubscriptionsCardWidget extends ConsumerWidget {
  final StudentModel student;
  final bool isPaidTab;
  final ValueNotifier<List<StudentModel>> paidStudents;
  final ValueNotifier<List<StudentModel>> unPaidStudents;
  final Function setState;

  const SubscriptionsCardWidget({
    super.key,
    required this.student,
    required this.isPaidTab,
    required this.paidStudents,
    required this.unPaidStudents,
    required this.setState,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.read(studentChangeNotifierProvider(context));

    final currentMonthStudentSubscription =
        student.subscriptions.firstWhereOrNull(
      (element) => element.date.isCurrentMonth,
    );

    return Stack(
      alignment: Alignment.topRight,
      children: [
        BaseContainer(
            padding: AppSpaces.appbarPadding,
            child: Row(
              children: [
                //! Subscription (Name - Date)
                Row(
                  children: [
                    SizedBox(
                      height: 50.h,
                      width: 50.w,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(
                            AppRadius.baseContainerRadius),
                        child: Image.network(
                          student.image?.url ?? '',
                          height: 50.h,
                          width: 50.w,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              Image.network(
                            AppConsts.studentPlaceholder,
                          ),
                        ),
                      ),
                    ),
                    context.smallGap,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //! Subscription Name
                        Text(
                          student.name,
                          style: context.blueHint
                              .copyWith(fontWeight: FontWeight.bold),
                        ),

                        if (currentMonthStudentSubscription != null) ...[
                          context.smallGap,

                          //! Subscription Date
                          Text(
                            currentMonthStudentSubscription.date,
                            style: context.hint.copyWith(fontSize: 12),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),

                const Spacer(),

                //! Subscription Price

                Text(
                  isPaidTab
                      ? ' \$${currentMonthStudentSubscription?.amount ?? 0}'
                      : ' \$${student.fees ?? NurseryModelHelper.currentNursery()?.fees ?? 0}',
                  style: context.priceTitle,
                ),

                context.mediumGap,

                if (!isPaidTab)
                  IconButton(
                    onPressed: () {
                      QuickAlert.show(
                        context: context,
                        title: context.tr.warning,
                        text: context.tr.areYouSureToMakeThisSubscriptionPaid,
                        type: QuickAlertType.info,
                        confirmBtnText: context.tr.confirm,
                        cancelBtnText: context.tr.cancel,
                        showCancelBtn: true,
                        confirmBtnColor: ColorManager.buttonColor,
                        onConfirmBtnTap: () async {
                          Navigator.of(context).pop();

                          final subscriptions = [
                            ...student.subscriptions,
                            SubscriptionModel(
                              amount: student.fees ??
                                  NurseryModelHelper.currentNursery()?.fees ??
                                  0,
                              date: DateTime.now().formatDateToString,
                              isPaid: true,
                            ),
                          ];

                          await studentCtrl.paySubscription(
                            studentId: student.id!,
                            subscriptions: subscriptions,
                            navigateWidget: const FinancialScreen(),
                          );

                          final copiedStudent = student.copyWith(
                            subscriptions: subscriptions,
                          );

                          context.showBarMessage(context.tr.paidSuccessfully);

                          paidStudents.value.insert(0, copiedStudent);
                          unPaidStudents.value.removeWhere(
                            (element) => element.id == student.id,
                          );

                          setState(() {});
                        },
                      );
                    },
                    icon: const Icon(
                      Icons.done_all,
                      color: ColorManager.buttonColor,
                    ),
                  ),
              ],
            ).paddingOnly(
              right: AppSpaces.mediumPadding,
            )),
      ],
    );
  }

  void showAcceptSubscriptionDialog(
    BuildContext context, {
    required WidgetRef ref,
  }) {}
}
