import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/screens/plan/view/widgets/plan_fields.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class AddPlanScreen extends HookConsumerWidget {
  final PlanModel? plan;

  const AddPlanScreen({super.key, this.plan});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planCtrl = ref.watch(planChangeNotifierControllerProvider(context));

    final formKey = useState(GlobalKey<FormState>());

    final controllers = <String, TextEditingController>{};

    final selectedClass = useState<ClassModel?>(plan?.classModel);

    // Initialize sections with existing plan sections or one empty section
    final sections = useState<List<PlanSection>>(
      plan?.sections.isNotEmpty == true
          ? List.from(plan!.sections)
          : [const PlanSection()],
    );

    Future<void> handleSubmit() async {
      if (selectedClass.value == null) {
        context.showBarMessage(context.tr.selectClass, isError: true);
        return;
      }

      if (formKey.value.currentState?.validate() ?? false) {
        // Filter out empty sections
        final validSections = sections.value
            .where((section) =>
                section.title.trim().isNotEmpty ||
                section.description.trim().isNotEmpty)
            .toList();

        if (plan != null) {
          // Edit existing plan
          await planCtrl.editPlan(
            id: plan!.id!,
            sections: validSections,
            classModel: selectedClass.value,
          );
        } else {
          // Add new plan
          await planCtrl.addPlan(
            sections: validSections,
            classModel: selectedClass.value,
          );
        }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title: plan != null ? context.tr.editPlan : context.tr.addPlan,
        iconPath: '',
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Form(
          key: formKey.value,
          child: Column(
            children: [
              Expanded(
                child: PlanFields(
                  controllers: controllers,
                  selectedClass: selectedClass,
                  sections: sections,
                ),
              ),
              context.largeGap,
              Button(
                loadingWidget: const LoadingWidget(),
                isLoading: planCtrl.isLoading,
                onPressed: handleSubmit,
                label: plan != null ? context.tr.update : context.tr.add,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
