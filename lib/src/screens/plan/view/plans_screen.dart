import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/base_date_filter_widget.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/view/add_plan_screen.dart';
import 'package:connectify_app/src/screens/plan/view/widgets/plan_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/widgets/shared_widgets.dart';
import '../../auth/models/user_model.dart';
import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class PlansScreen extends HookConsumerWidget {
  const PlansScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = useState<DateTime>(DateTime.now());

    final params = (context, selectedDate.value.formatToMonthName);

    final plansData = ref.watch(getPlansDataByMonthProvider(params));

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          isBackButton: true,
          title: context.tr.plans,
          iconPath: '',
        ),
        body: Column(
          children: [
            if (const UserModel().isAdmin)
              Padding(
                padding: const EdgeInsets.all(AppSpaces.largePadding),
                child: AddRectangleWidget(
                    title: context.tr.addAnnouncement,
                    onTap: () {
                      context.to(const AddPlanScreen());
                    }),
              ),

            // Month filter
            BaseDateFilterWidget(
              date: selectedDate.value,
              onNext: () {
                selectedDate.value = DateTime(
                  selectedDate.value.year,
                  selectedDate.value.month + 1,
                );
              },
              onPrevious: () {
                selectedDate.value = DateTime(
                  selectedDate.value.year,
                  selectedDate.value.month - 1,
                );
              },
            ),

            // Plans list
            Expanded(
              child: plansData.get(
                data: (plans) {
                  if (plans.isEmpty) {
                    return Center(
                      child: Text(
                        context.tr.noPlansForThisMonth,
                        style: context.subHeadLine,
                      ),
                    );
                  }

                  return BaseList(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    data: plans,
                    itemBuilder: (plan, index) => PlanCard(
                      plan: plan,
                      onEdit: () {
                        context.to(AddPlanScreen(plan: plan));
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
