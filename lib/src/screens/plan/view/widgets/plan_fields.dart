import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/screens/plan/view/widgets/plan_section_widget.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<ClassModel?> selectedClass;
  final ValueNotifier<DateTime> selectedDate;
  final ValueNotifier<List<PlanSection>> sections;

  const PlanFields({
    super.key,
    required this.controllers,
    required this.selectedClass,
    required this.selectedDate,
    required this.sections,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title field
          BaseTextField(
            title: context.tr.title,
            controller: controllers[ApiStrings.title],
            textInputType: TextInputType.text,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return context.tr.thisFieldIsRequired;
              }
              return null;
            },
          ),

          context.fieldsGap,

          // Description field
          BaseTextField(
            title: context.tr.description,
            controller: controllers[ApiStrings.description],
            textInputType: TextInputType.multiline,
            maxLines: 3,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return context.tr.thisFieldIsRequired;
              }
              return null;
            },
          ),

          context.fieldsGap,

          // Class dropdown
          ClassDropDown(
            selectedClass: selectedClass,
            selectFirstClass: false,
          ),

          context.fieldsGap,

          // Date picker
          BaseDatePicker(
            selectedDateNotifier: selectedDate,
            label: context.tr.date,
          ),

          context.fieldsGap,

          // Sections header
          Row(
            children: [
              Text(
                context.tr.sections,
                style: context.labelLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () {
                  sections.value = [
                    ...sections.value,
                    const PlanSection(),
                  ];
                },
                icon: const Icon(Icons.add),
                label: Text(context.tr.addNewSection),
              ),
            ],
          ),

          context.smallGap,

          // Sections list
          ValueListenableBuilder<List<PlanSection>>(
            valueListenable: sections,
            builder: (context, sectionsList, child) {
              return Column(
                children: sectionsList.asMap().entries.map((entry) {
                  final index = entry.key;
                  final section = entry.value;

                  return PlanSectionWidget(
                    key: ValueKey('section_$index'),
                    section: section,
                    index: index,
                    onChanged: (updatedSection) {
                      final updatedSections = List<PlanSection>.from(sections.value);
                      updatedSections[index] = updatedSection;
                      sections.value = updatedSections;
                    },
                    onRemove: sectionsList.length > 1
                        ? () {
                            final updatedSections = List<PlanSection>.from(sections.value);
                            updatedSections.removeAt(index);
                            sections.value = updatedSections;
                          }
                        : null,
                  );
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }
}
