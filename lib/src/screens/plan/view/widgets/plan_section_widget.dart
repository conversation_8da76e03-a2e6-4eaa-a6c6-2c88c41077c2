import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanSectionWidget extends HookWidget {
  final PlanSection section;
  final int index;
  final Function(PlanSection) onChanged;
  final VoidCallback? onRemove;

  const PlanSectionWidget({
    super.key,
    required this.section,
    required this.index,
    required this.onChanged,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final titleController = useTextEditingController(text: section.title);
    final descriptionController = useTextEditingController(text: section.description);

    // Listen to changes and update the section
    useEffect(() {
      void listener() {
        onChanged(PlanSection(
          title: titleController.text,
          description: descriptionController.text,
        ));
      }

      titleController.addListener(listener);
      descriptionController.addListener(listener);

      return () {
        titleController.removeListener(listener);
        descriptionController.removeListener(listener);
      };
    }, [titleController, descriptionController]);

    return BaseContainer(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Row(
            children: [
              Text(
                '${context.tr.section} ${index + 1}',
                style: context.labelMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (onRemove != null)
                IconButton(
                  onPressed: onRemove,
                  icon: const Icon(
                    Icons.delete,
                    color: Colors.red,
                    size: 20,
                  ),
                  tooltip: context.tr.removeSection,
                ),
            ],
          ),

          context.smallGap,

          // Section title
          BaseTextField(
            title: context.tr.sectionTitle,
            controller: titleController,
            textInputType: TextInputType.text,
            hintText: context.tr.enterSectionTitle,
          ),

          context.fieldsGap,

          // Section description
          BaseTextField(
            title: context.tr.sectionDescription,
            controller: descriptionController,
            textInputType: TextInputType.multiline,
            maxLines: 3,
            hintText: context.tr.enterSectionDescription,
          ),
        ],
      ),
    );
  }
}
