import 'package:connectify_app/src/screens/plan/controller/plan_controller.dart';
import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class PlanCard extends HookConsumerWidget {
  final PlanModel plan;
  final VoidCallback? onEdit;

  const PlanCard({
    super.key,
    required this.plan,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final planCtrl = ref.watch(planChangeNotifierControllerProvider(context));

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          Row(
            children: [
              Expanded(
                child: Text(
                  plan.title,
                  style: context.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // Edit button
              IconButton(
                onPressed: onEdit,
                icon: const Icon(Icons.edit),
                tooltip: context.tr.edit,
              ),
              // Delete button
              IconButton(
                onPressed: () {
                  _showDeleteDialog(context, planCtrl);
                },
                icon: const Icon(Icons.delete, color: Colors.red),
                tooltip: context.tr.delete,
              ),
            ],
          ),

          context.smallGap,

          // Class and date info
          Row(
            children: [
              if (plan.classModel != null) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue),
                  ),
                  child: Text(
                    plan.classModel!.name,
                    style: context.labelSmall?.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                context.smallGap,
              ],
              if (plan.date != null)
                Text(
                  plan.date!.formatDateToString,
                  style: context.labelSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),

          context.mediumGap,

          // Description
          if (plan.description.isNotEmpty) ...[
            Text(
              plan.description,
              style: context.bodyMedium,
            ),
            context.mediumGap,
          ],

          // Sections
          if (plan.sections.isNotEmpty) ...[
            Text(
              context.tr.sections,
              style: context.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            context.smallGap,
            ...plan.sections.asMap().entries.map((entry) {
              final index = entry.key;
              final section = entry.value;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (section.title.isNotEmpty) ...[
                      Text(
                        '${index + 1}. ${section.title}',
                        style: context.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (section.description.isNotEmpty) context.smallGap,
                    ],
                    if (section.description.isNotEmpty)
                      Text(
                        section.description,
                        style: context.bodySmall,
                      ),
                  ],
                ),
              );
            }).toList(),
          ],

          context.mediumGap,

          // Footer with teacher name and created date
          Row(
            children: [
              if (plan.teacher != null) ...[
                Text(
                  '${context.tr.by}: ${plan.teacher!.name}',
                  style: context.labelSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
              ],
              if (plan.createdAt != null)
                Text(
                  plan.createdAt!.formatDateToString,
                  style: context.labelSmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, PlanController controller) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.tr.deletePlan),
          content: Text(context.tr.areYouSureYouWantToDeleteThisPlan),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr.cancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await controller.deletePlan(id: plan.id!);
              },
              child: Text(
                context.tr.delete,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
