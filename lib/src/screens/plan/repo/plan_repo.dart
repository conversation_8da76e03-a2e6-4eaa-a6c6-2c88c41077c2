import 'package:connectify_app/src/screens/plan/model/plan_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final planRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return PlanRepo(networkApiServices);
});

class PlanRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  PlanRepo(this._networkApiServices);

  //? Get Plans Data ========================================================
  Future<List<PlanModel>> getPlans() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.plans,
      );

      final planData = await compute(responseToPlanModelList, response);

      return planData;
    });
  }

  //? Get Plans Data by Month ========================================================
  Future<List<PlanModel>> getPlansByMonth({
    required String monthName,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.plans}&filters[date][\$containsi]=$monthName',
      );

      final planData = await compute(responseToPlanModelList, response);

      return planData;
    });
  }

  //? Get Plans Data with Pagination ========================================================
  Future<List<PlanModel>> getPlansPaginated({
    int page = 1,
  }) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        '${ApiEndpoints.plans}&${ApiEndpoints.pagination(page)}',
      );

      final planData = await compute(responseToPlanModelList, response);

      return planData;
    });
  }

  //? Add Plan ========================================================
  Future<void> addPlan({required PlanModel plan}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(
        ApiEndpoints.editDeletePlan,
        body: plan.toJson(),
      );
    });
  }

  //? Edit Plan ========================================================
  Future<void> editPlan({
    required int id,
    required PlanModel plan,
  }) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
        '${ApiEndpoints.editDeletePlan}/$id',
        body: plan.toJson(),
      );
    });
  }

  //? Delete Plan ========================================================
  Future<void> deletePlan({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices.deleteResponse(
        '${ApiEndpoints.editDeletePlan}/$id',
      );
    });
  }
}
