import 'package:connectify_app/src/screens/auth/view/widgets/background_and_logo.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../sign_up_screen/sign_up_as/sign_up_as_screen.dart';
import 'widgets/sign_in_button.dart';
import 'widgets/sign_in_fields.dart';

class SignInScreen extends HookWidget {
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controllers = {
      ApiStrings.phone: useTextEditingController(
        //? Admin
        // text: kDebugMode ? '01129691263' : null,
        text: kDebugMode ? '01014878502' : null,
        //? Teacher
      ),
      ApiStrings.password: useTextEditingController(
        text: kDebugMode ? '123456789' : null,
      ),
    };

    final formKey = useState(GlobalKey<FormState>());

    return WillPopScope(
      onWillPop: () async {
        SystemNavigator.pop();
        return false;
      },
      child: BackgroundLogo(
        isSignIn: true,
        child: Form(
          key: formKey.value,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              //! Sign In Fields
              SignInFields(
                controllers: controllers,
              ),

              //! Sign In Button
              LoginButton(controllers: controllers, formKey: formKey.value),

              context.mediumGap,

              //! Don't have an account
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    context.tr.dontHaveAnAccount,
                    style: context.subTitle,
                  ),
                  TextButton(
                      onPressed: () {
                        context.to(const SignUpAsScreen());
                      },
                      child: Text(
                        context.tr.signUp,
                        style: context.subTitle
                            .copyWith(color: ColorManager.primaryColor),
                      )),
                ],
              ),
            ],
          ).paddingAll(AppSpaces.mediumPadding),
        ),
      ),
    );
  }
}
