import 'package:connectify_app/firebase_options.dart';
import 'package:connectify_app/src/app.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:xr_helper/xr_helper.dart';

//? * Announcment (teacher - parents - both) view - show - resend
//? Monthly Plan (title - description - add section - class option) - add edit delete
//? Update Register Logo as admin on iOS

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  String storageLocation = (await getApplicationDocumentsDirectory()).path;

  await Future.wait([
    FastCachedImageConfig.init(
      subDir: storageLocation,
    ),
    Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ),
    GetStorageService.init(),
    // GetStorageService.clearLocalData(),
  ]);

  NotificationService.init();

  runApp(const ProviderScope(child: BaseApp()));
}
